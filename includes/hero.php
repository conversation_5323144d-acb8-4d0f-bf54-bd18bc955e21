  <!-- Hero Section -->
  <section class="relative overflow-hidden bg-gradient-to-br from-beige-50 via-white to-rose-50 py-20 lg:py-32">
    <!-- Background Decorative Elements -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-rose-200/30 to-skin-200/30 rounded-full blur-3xl"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-beige-200/40 to-primary-200/30 rounded-full blur-3xl"></div>
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-skin-100/20 to-rose-100/20 rounded-full blur-3xl"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">

        <!-- Left Content -->
        <div class="text-center lg:text-left space-y-8 animate-slide-up">
          <!-- Badge -->
          <div class="inline-flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full shadow-soft border border-beige-200">
            <svg class="w-4 h-4 text-rose-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span class="text-sm font-medium text-gray-700">100% Natürlich & Vegan</span>
          </div>

          <!-- Main Heading -->
          <div class="space-y-4">
            <h1 class="text-4xl sm:text-5xl lg:text-6xl font-serif font-bold leading-tight">
              <span class="gradient-text">Natürliche</span><br>
              <span class="text-gray-800">Aromatherapie-</span><br>
              <span class="gradient-text">Kosmetik</span>
            </h1>
            <div class="w-24 h-1 bg-gradient-to-r from-rose-400 to-skin-400 rounded-full mx-auto lg:mx-0"></div>
          </div>

          <!-- Description -->
          <p class="text-lg lg:text-xl text-gray-600 leading-relaxed max-w-xl mx-auto lg:mx-0">
            Sanfte Pflege für Haut & Sinne – vegan, tierversuchsfrei & inspiriert von der Kraft ätherischer Öle.
          </p>

          <!-- Key Features -->
          <div class="flex flex-wrap justify-center lg:justify-start gap-3">
            <div class="flex items-center px-3 py-2 bg-white/60 backdrop-blur-sm rounded-lg border border-beige-200">
              <svg class="w-4 h-4 text-rose-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <span class="text-sm font-medium text-gray-700">Vegan</span>
            </div>
            <div class="flex items-center px-3 py-2 bg-white/60 backdrop-blur-sm rounded-lg border border-beige-200">
              <svg class="w-4 h-4 text-skin-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
              </svg>
              <span class="text-sm font-medium text-gray-700">Tierversuchsfrei</span>
            </div>
            <div class="flex items-center px-3 py-2 bg-white/60 backdrop-blur-sm rounded-lg border border-beige-200">
              <svg class="w-4 h-4 text-primary-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <span class="text-sm font-medium text-gray-700">Nachhaltig</span>
            </div>
          </div>

          <!-- CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start pt-4">
            <a href="#produkte" class="group inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-rose-400 to-skin-400 text-white font-semibold rounded-2xl shadow-medium hover:shadow-large hover:-translate-y-1 transition-all duration-300">
              <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4l1-12z"/>
              </svg>
              Produkte entdecken
            </a>
            <a href="https://www.versandapotheke.de/search/result.html?term=larome" target="_blank" class="group inline-flex items-center justify-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-2xl shadow-soft hover:shadow-medium border-2 border-beige-200 hover:border-primary-300 hover:-translate-y-1 transition-all duration-300">
              <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
              </svg>
              Direkt zum Shop
            </a>
          </div>
        </div>

        <!-- Right Content - Hero Image -->
        <div class="relative lg:order-last">
          <div class="relative">
            <!-- Main Hero Image -->
            <div class="relative overflow-hidden rounded-3xl shadow-large">
              <img src="https://images.unsplash.com/photo-1596755389378-c31d21fd1273?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                   alt="Natürliche Aromatherapie Kosmetik"
                   class="w-full h-96 lg:h-[500px] object-cover">
              <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>

            <!-- Floating Cards -->
            <div class="absolute -top-6 -left-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-large float-animation">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-rose-400 to-skin-400 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </div>
                <div>
                  <p class="font-semibold text-gray-800">100% Vegan</p>
                  <p class="text-sm text-gray-600">Pflanzlich & ethisch</p>
                </div>
              </div>
            </div>

            <div class="absolute -bottom-6 -right-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-large float-animation" style="animation-delay: 2s;">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-primary-400 to-beige-400 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <p class="font-semibold text-gray-800">Premium Qualität</p>
                  <p class="text-sm text-gray-600">Made in Germany</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Trust Indicators -->
      <div class="mt-16 lg:mt-24">
        <p class="text-center text-sm text-gray-500 mb-8">Vertraut von über 10.000+ zufriedenen Kunden</p>
        <div class="flex flex-wrap justify-center items-center gap-8 lg:gap-12 opacity-60">
          <!-- Trust badges/logos would go here -->
          <div class="flex items-center space-x-2">
            <svg class="w-6 h-6 text-rose-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span class="text-sm font-medium text-gray-600">Dermatologisch getestet</span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-6 h-6 text-skin-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="text-sm font-medium text-gray-600">Klimaneutral versendet</span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-6 h-6 text-primary-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
            </svg>
            <span class="text-sm font-medium text-gray-600">30 Tage Geld-zurück</span>
          </div>
        </div>
      </div>
    </div>
  </section>