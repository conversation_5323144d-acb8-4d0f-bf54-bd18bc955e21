<!DOCTYPE html>
<html lang="de">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <title>LAROME Aromatherapie-Kosmetik | Natürliche Pflege</title>
  <meta name="description" content="Entdecke LAROME: vegane, nat<PERSON>rl<PERSON>, inspiriert von der Aromatherapie. Für empfindliche Haut, tierversuchsfrei & nachhaltig produziert." />
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            // Primary Brand Colors
            primary: {
              50: '#fdf7f0',
              100: '#fbeee0',
              200: '#f6dcc1',
              300: '#f0c397',
              400: '#e8a56b',
              500: '#e2924a',
              600: '#d4803f',
              700: '#b16835',
              800: '#8e5530',
              900: '#734729',
            },
            // <PERSON>
            rose: {
              50: '#fff8f8',
              100: '#fff0f0',
              200: '#ffe4e6',
              300: '#ffcdd2',
              400: '#ffaab0',
              500: '#ff8a95',
              600: '#f56565',
              700: '#e53e3e',
              800: '#c53030',
              900: '#9b2c2c',
            },
            // Beige & Neutral Tones
            beige: {
              50: '#fefdfb',
              100: '#fdf9f3',
              200: '#faf5eb',
              300: '#f5ede0',
              400: '#ede1d0',
              500: '#e3d2bb',
              600: '#d4c0a1',
              700: '#bfa888',
              800: '#a08968',
              900: '#7d6b4f',
            },
            // Skin Tones
            skin: {
              50: '#fef9f6',
              100: '#fdf2ec',
              200: '#fae5d6',
              300: '#f6d4bb',
              400: '#f0bd95',
              500: '#e8a474',
              600: '#de8a56',
              700: '#d1713f',
              800: '#b85d34',
              900: '#954c2e',
            },
            // Accent Colors
            accent: {
              gold: '#f7e7ce',
              cream: '#faf8f5',
              blush: '#f8e8e8',
              nude: '#f2e6d9',
            }
          },
          fontFamily: {
            // Elegant serif for headings
            serif: ['Playfair Display', 'Georgia', 'serif'],
            // Clean sans-serif for body text
            sans: ['Inter', 'system-ui', 'sans-serif'],
            // Script font for special elements
            script: ['Dancing Script', 'cursive'],
          },
          fontSize: {
            'xs': ['0.75rem', {
              lineHeight: '1rem'
            }],
            'sm': ['0.875rem', {
              lineHeight: '1.25rem'
            }],
            'base': ['1rem', {
              lineHeight: '1.5rem'
            }],
            'lg': ['1.125rem', {
              lineHeight: '1.75rem'
            }],
            'xl': ['1.25rem', {
              lineHeight: '1.75rem'
            }],
            '2xl': ['1.5rem', {
              lineHeight: '2rem'
            }],
            '3xl': ['1.875rem', {
              lineHeight: '2.25rem'
            }],
            '4xl': ['2.25rem', {
              lineHeight: '2.5rem'
            }],
            '5xl': ['3rem', {
              lineHeight: '1'
            }],
            '6xl': ['3.75rem', {
              lineHeight: '1'
            }],
          },
          boxShadow: {
            'soft': '0 2px 15px 0 rgba(0, 0, 0, 0.08)',
            'medium': '0 4px 25px 0 rgba(0, 0, 0, 0.12)',
            'large': '0 8px 40px 0 rgba(0, 0, 0, 0.15)',
            'rose': '0 4px 24px 0 rgba(255, 138, 149, 0.15)',
            'beige': '0 4px 24px 0 rgba(227, 210, 187, 0.20)',
            'inner-soft': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
          },
          borderRadius: {
            'xl': '1rem',
            '2xl': '1.5rem',
            '3xl': '2rem',
          },
          spacing: {
            '18': '4.5rem',
            '88': '22rem',
            '128': '32rem',
          },
          animation: {
            'fade-in': 'fadeIn 0.5s ease-in-out',
            'slide-up': 'slideUp 0.6s ease-out',
            'float': 'float 3s ease-in-out infinite',
          },
          keyframes: {
            fadeIn: {
              '0%': {
                opacity: '0'
              },
              '100%': {
                opacity: '1'
              },
            },
            slideUp: {
              '0%': {
                transform: 'translateY(20px)',
                opacity: '0'
              },
              '100%': {
                transform: 'translateY(0)',
                opacity: '1'
              },
            },
            float: {
              '0%, 100%': {
                transform: 'translateY(0px)'
              },
              '50%': {
                transform: 'translateY(-10px)'
              },
            },
          },
        }
      }
    }
  </script>
  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="icon" type="image/png" href="https://img.icons8.com/fluency/48/000000/spa-flower.png" />
  <style>
    /* Custom Scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: #faf8f5;
    }

    ::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #ff8a95, #e8a474);
      border-radius: 10px;
      border: 2px solid #faf8f5;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(135deg, #f56565, #de8a56);
    }

    /* Smooth scrolling */
    html {
      scroll-behavior: smooth;
    }

    /* Body styling */
    body {
      background: linear-gradient(135deg, #fefdfb 0%, #fdf9f3 100%);
      font-feature-settings: "kern" 1, "liga" 1;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Selection styling */
    ::selection {
      background-color: #ffcdd2;
      color: #734729;
    }

    ::-moz-selection {
      background-color: #ffcdd2;
      color: #734729;
    }

    /* Details arrow smooth rotation for FAQ */
    details>summary {
      cursor: pointer;
      list-style: none;
    }

    details>summary::-webkit-details-marker {
      display: none;
    }

    details>summary span:last-child {
      transition: transform 0.3s ease;
    }

    details[open]>summary span:last-child {
      transform: rotate(180deg);
    }

    /* Button hover effects */
    .btn-primary {
      background: linear-gradient(135deg, #e8a474, #de8a56);
      transition: all 0.3s ease;
    }

    .btn-primary:hover {
      background: linear-gradient(135deg, #de8a56, #d1713f);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(222, 138, 86, 0.3);
    }

    /* Floating animation for decorative elements */
    .float-animation {
      animation: float 6s ease-in-out infinite;
    }

    /* Gradient text */
    .gradient-text {
      background: linear-gradient(135deg, #e8a474, #ff8a95);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    /* Glass morphism effect */
    .glass {
      background: rgba(255, 255, 255, 0.25);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.18);
    }
  </style>
  <script data-host="https://analytics.apotheken.services" data-dnt="false" src="https://analytics.apotheken.services/js/script.js" id="ZwSg9rf6GA" async defer></script>

  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>

<body class="bg-gradient-to-br from-beige-50 to-beige-100 text-gray-800 font-sans min-h-screen">

  <!-- Header -->
  <header class="relative bg-white/80 backdrop-blur-md border-b border-beige-200/50 sticky top-0 z-50 transition-all duration-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-20">
      <div class="flex justify-between items-center h-20">

        <!-- Logo -->
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-rose-400 to-skin-400 rounded-xl flex items-center justify-center shadow-soft">
            <svg class="w-6 h-6 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
              <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
              <g id="SVGRepo_iconCarrier">
                <path d="M3.84453 3.84453C2.71849 4.97056 2.71849 6.79623 3.84453 7.92226L5.43227 9.51C5.44419 9.49622 5.45669 9.48276 5.46978 9.46967L9.46978 5.46967C9.48284 5.45662 9.49625 5.44415 9.50999 5.43226L7.92226 3.84453C6.79623 2.71849 4.97056 2.71849 3.84453 3.84453Z" fill="currentColor"></path>
                <path d="M10.5679 6.49012C10.556 6.50386 10.5435 6.51728 10.5304 6.53033L6.53044 10.5303C6.51735 10.5434 6.5039 10.5559 6.49011 10.5678L16.0777 20.1555C17.2038 21.2815 19.0294 21.2815 20.1555 20.1555C21.2815 19.0294 21.2815 17.2038 20.1555 16.0777L10.5679 6.49012Z" fill="currentColor"></path>
                <path d="M16.1 2.30719C16.261 1.8976 16.8385 1.8976 16.9994 2.30719L17.4298 3.40247C17.479 3.52752 17.5776 3.62651 17.7022 3.67583L18.7934 4.1078C19.2015 4.26934 19.2015 4.849 18.7934 5.01054L17.7022 5.44252C17.5776 5.49184 17.479 5.59082 17.4298 5.71587L16.9995 6.81115C16.8385 7.22074 16.261 7.22074 16.1 6.81116L15.6697 5.71587C15.6205 5.59082 15.5219 5.49184 15.3973 5.44252L14.3061 5.01054C13.898 4.849 13.898 4.26934 14.3061 4.1078L15.3973 3.67583C15.5219 3.62651 15.6205 3.52752 15.6697 3.40247L16.1 2.30719Z" fill="currentColor"></path>
                <path d="M19.9672 9.12945C20.1281 8.71987 20.7057 8.71987 20.8666 9.12945L21.0235 9.5288C21.0727 9.65385 21.1713 9.75284 21.2959 9.80215L21.6937 9.95965C22.1018 10.1212 22.1018 10.7009 21.6937 10.8624L21.2959 11.0199C21.1713 11.0692 21.0727 11.1682 21.0235 11.2932L20.8666 11.6926C20.7057 12.1022 20.1281 12.1022 19.9672 11.6926L19.8103 11.2932C19.7611 11.1682 19.6625 11.0692 19.5379 11.0199L19.14 10.8624C18.732 10.7009 18.732 10.1212 19.14 9.95965L19.5379 9.80215C19.6625 9.75284 19.7611 9.65385 19.8103 9.5288L19.9672 9.12945Z" fill="currentColor"></path>
                <path d="M5.1332 15.3072C5.29414 14.8976 5.87167 14.8976 6.03261 15.3072L6.18953 15.7065C6.23867 15.8316 6.33729 15.9306 6.46188 15.9799L6.85975 16.1374C7.26783 16.2989 7.26783 16.8786 6.85975 17.0401L6.46188 17.1976C6.33729 17.2469 6.23867 17.3459 6.18953 17.471L6.03261 17.8703C5.87167 18.2799 5.29414 18.2799 5.1332 17.8703L4.97628 17.471C4.92714 17.3459 4.82852 17.2469 4.70393 17.1976L4.30606 17.0401C3.89798 16.8786 3.89798 16.2989 4.30606 16.1374L4.70393 15.9799C4.82852 15.9306 4.92714 15.8316 4.97628 15.7065L5.1332 15.3072Z" fill="currentColor"></path>
              </g>
            </svg>
          </div>
          <div class="flex flex-col">
            <span class="text-2xl font-serif font-bold gradient-text tracking-tight">LAROME</span>
            <span class="text-xs text-gray-500 font-medium tracking-wide">AROMATHERAPIE</span>
          </div>
        </div>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex items-center space-x-8">
          <a href="#produkte" class="relative text-gray-700 hover:text-primary-600 font-medium transition-all duration-300 group">
            Produkte
            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-rose-400 to-skin-400 transition-all duration-300 group-hover:w-full"></span>
          </a>
          <a href="#philosophie" class="relative text-gray-700 hover:text-primary-600 font-medium transition-all duration-300 group">
            Philosophie
            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-rose-400 to-skin-400 transition-all duration-300 group-hover:w-full"></span>
          </a>
          <a href="#wirkstoffe" class="relative text-gray-700 hover:text-primary-600 font-medium transition-all duration-300 group">
            Wirkstoffe
            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-rose-400 to-skin-400 transition-all duration-300 group-hover:w-full"></span>
          </a>
          <a href="#kontakt" class="relative text-gray-700 hover:text-primary-600 font-medium transition-all duration-300 group">
            Kontakt
            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-rose-400 to-skin-400 transition-all duration-300 group-hover:w-full"></span>
          </a>
        </nav>

        <!-- CTA Button & Mobile Menu Button -->
        <div class="flex items-center space-x-4">
          <!-- CTA Button (hidden on mobile) -->
          <a href="#kontakt" class="hidden sm:inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-rose-400 to-skin-400 text-white font-medium rounded-xl hover:from-rose-500 hover:to-skin-500 transition-all duration-300 shadow-soft hover:shadow-medium hover:-translate-y-0.5">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            Beratung
          </a>

          <!-- Mobile Menu Button -->
          <button id="mobile-menu-button" class="md:hidden p-2 rounded-lg text-gray-700 hover:text-primary-600 hover:bg-beige-100 transition-colors duration-200">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="md:hidden absolute top-full left-0 right-0 bg-white/95 backdrop-blur-md border-b border-beige-200/50 shadow-large transform -translate-y-full opacity-0 transition-all duration-300 ease-out z-10">
      <div class="px-4 py-6 space-y-4">
        <a href="#produkte" class="block px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-beige-50 rounded-lg font-medium transition-colors duration-200">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-3 text-rose-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z" />
            </svg>
            Produkte
          </div>
        </a>
        <a href="#philosophie" class="block px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-beige-50 rounded-lg font-medium transition-colors duration-200">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-3 text-skin-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
            </svg>
            Philosophie
          </div>
        </a>
        <a href="#wirkstoffe" class="block px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-beige-50 rounded-lg font-medium transition-colors duration-200">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-3 text-primary-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9.8 2h4.4c.8 0 1.5.3 2.1.9l2.8 2.8c.6.6.9 1.3.9 2.1v4.4c0 .8-.3 1.5-.9 2.1l-2.8 2.8c-.6.6-1.3.9-2.1.9H9.8c-.8 0-1.5-.3-2.1-.9L4.9 14.3c-.6-.6-.9-1.3-.9-2.1V7.8c0-.8.3-1.5.9-2.1L7.7 2.9C8.3 2.3 9 2 9.8 2z" />
            </svg>
            Wirkstoffe
          </div>
        </a>
        <a href="#kontakt" class="block px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-beige-50 rounded-lg font-medium transition-colors duration-200">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-3 text-rose-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            Kontakt
          </div>
        </a>
        <div class="pt-4 border-t border-beige-200">
          <a href="#kontakt" class="block w-full px-4 py-3 bg-gradient-to-r from-rose-400 to-skin-400 text-white font-medium rounded-lg text-center hover:from-rose-500 hover:to-skin-500 transition-all duration-300">
            Kostenlose Beratung
          </a>
        </div>
      </div>
    </div>
  </header>


  <?php include('includes/hero.php') ?>

  <?php include('includes/philosophie.php') ?>

  <?php include('includes/kundenstimmen.php') ?>

  <?php include('includes/wirkstoffe.php') ?>

  <?php include('includes/produkte.php') ?>

  <?php include('includes/faq.php') ?>

  <?php include('includes/kontakt.php') ?>


  <footer class="bg-gradient-to-br from-beige-50 to-white border-t border-beige-200 pt-12 pb-8 mt-16 text-gray-700">
    <div class="max-w-6xl mx-auto px-4 flex flex-col md:flex-row md:justify-between gap-8">

      <!-- Logo + Slogan -->
      <div class="flex-1 flex flex-col gap-2 items-start md:items-start">
        <span class="text-2xl font-serif font-bold gradient-text">LAROME</span>
        <span class="text-sm text-gray-500">Aromatherapie-Kosmetik<br>Natürlich. Vegan. Sensitiv.</span>
      </div>

      <!-- Navigation + Social -->
      <div class="flex-1 flex flex-col items-center gap-3">
        <nav class="flex flex-wrap gap-6 justify-center mb-2">
          <a href="#produkte" class="hover:text-primary-600 transition-colors duration-200">Produkte</a>
          <a href="#wirkstoffe" class="hover:text-primary-600 transition-colors duration-200">Wirkstoffe</a>
          <a href="#kundenstimmen" class="hover:text-primary-600 transition-colors duration-200">Kunden</a>
          <a href="#philosophie" class="hover:text-primary-600 transition-colors duration-200">Philosophie</a>
          <a href="#faq" class="hover:text-primary-600 transition-colors duration-200">FAQ</a>
          <a href="#kontakt" class="hover:text-primary-600 transition-colors duration-200">Kontakt</a>
        </nav>
        <div class="flex gap-5 mt-2">
          <!-- Instagram -->
          <a href="https://www.instagram.com/dein_instagramname" target="_blank" rel="noopener" aria-label="Instagram"
            class="text-rose-500 hover:text-rose-600 transition-colors duration-200">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.849.07 1.366.062 2.633.35 3.608 1.324.974.974 1.262 2.241 1.324 3.608.058 1.265.07 1.645.07 4.849s-.012 3.584-.07 4.849c-.062 1.366-.35 2.633-1.324 3.608-.974.974-2.241 1.262-3.608 1.324-1.265.058-1.645.07-4.849.07s-3.584-.012-4.849-.07c-1.366-.062-2.633-.35-3.608-1.324-.974-.974-1.262-2.241-1.324-3.608C2.175 15.647 2.163 15.267 2.163 12s.012-3.584.07-4.849c.062-1.366.35-2.633 1.324-3.608C4.531 2.583 5.798 2.295 7.164 2.233 8.429 2.175 8.809 2.163 12 2.163zm0-2.163C8.735 0 8.333.015 7.052.072c-1.637.074-3.166.508-4.355 1.697C1.511 2.884 1.076 4.413 1.002 6.052.945 7.333.93 7.735.93 12s.015 4.667.072 5.948c.074 1.639.509 3.167 1.698 4.356 1.189 1.188 2.718 1.623 4.355 1.697 1.281.057 1.683.072 5.948.072s4.667-.015 5.948-.072c1.639-.074 3.167-.509 4.356-1.697 1.188-1.189 1.623-2.717 1.697-4.356.057-1.281.072-1.683.072-5.948s-.015-4.667-.072-5.948c-.074-1.639-.509-3.167-1.697-4.356-1.189-1.189-2.717-1.623-4.356-1.697C15.667.015 15.265 0 12 0z" />
              <path d="M12 5.838a6.163 6.163 0 1 0 0 12.325 6.163 6.163 0 0 0 0-12.325zm0 10.163a3.999 3.999 0 1 1 0-7.997 3.999 3.999 0 0 1 0 7.997zm6.406-11.845a1.44 1.44 0 1 0 0 2.88 1.44 1.44 0 0 0 0-2.88z" />
            </svg>
          </a>
          <!-- Optional: E-Mail direkt als Icon -->
          <a href="mailto:<EMAIL>" aria-label="Mail" class="text-skin-500 hover:text-skin-600 transition-colors duration-200">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <rect width="20" height="14" x="2" y="5" rx="3" fill="none" />
              <path d="M2 7l10 6 10-6" />
            </svg>
          </a>
        </div>
      </div>

      <!-- Adresse & Rechtliches -->
      <div class="flex-1 flex flex-col gap-2 items-end text-right md:items-end">
        <span class="text-sm">LAROME Naturkosmetik GmbH<br>Beispielstraße 12<br>12345 Musterstadt</span>
        <span class="text-xs text-gray-500 mt-2">© 2024 LAROME – Alle Rechte vorbehalten</span>
        <div class="flex gap-4 justify-end mt-2 text-sm">
          <a href="/impressum.html" class="hover:text-primary-600 transition-colors duration-200">Impressum</a>
          <a href="/datenschutz.html" class="hover:text-primary-600 transition-colors duration-200">Datenschutz</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Mobile Menu JavaScript -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const mobileMenuButton = document.getElementById('mobile-menu-button');
      const mobileMenu = document.getElementById('mobile-menu');
      let isMenuOpen = false;

      mobileMenuButton.addEventListener('click', function() {
        isMenuOpen = !isMenuOpen;

        if (isMenuOpen) {
          mobileMenu.style.transform = 'translateY(0)';
          mobileMenu.style.opacity = '1';
          mobileMenuButton.innerHTML = `
            <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          `;
        } else {
          mobileMenu.style.transform = 'translateY(-100%)';
          mobileMenu.style.opacity = '0';
          mobileMenuButton.innerHTML = `
            <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
          `;
        }
      });

      // Close menu when clicking on a link
      const mobileMenuLinks = mobileMenu.querySelectorAll('a');
      mobileMenuLinks.forEach(link => {
        link.addEventListener('click', function() {
          isMenuOpen = false;
          mobileMenu.style.transform = 'translateY(-100%)';
          mobileMenu.style.opacity = '0';
          mobileMenuButton.innerHTML = `
            <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
          `;
        });
      });

      // Header scroll effect
      let lastScrollY = window.scrollY;
      window.addEventListener('scroll', function() {
        const header = document.querySelector('header');
        const currentScrollY = window.scrollY;

        if (currentScrollY > 100) {
          header.classList.add('shadow-medium');
          header.classList.remove('shadow-soft');
        } else {
          header.classList.remove('shadow-medium');
          header.classList.add('shadow-soft');
        }

        lastScrollY = currentScrollY;
      });
    });
  </script>

  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script>
    AOS.init();
  </script>
</body>

</html>